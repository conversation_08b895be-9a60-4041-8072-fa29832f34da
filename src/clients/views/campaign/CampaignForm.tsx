'use client';

import { useCallback, useMemo, useState } from 'react';

import { CalendarIcon } from '@heroicons/react/24/outline';
import { DateRange } from 'react-day-picker';
import { toast } from 'react-toastify';

import AmenitiesSelector from '@/clients/components/common/AmenitiesSelector';
import { GuestsValues } from '@/clients/components/common/GuestSelector';
import Button from '@/clients/ui/Button';
import { Input } from '@/components/ui/input';
import { EMAIL_PATTERN, PHONE_NUMBER_PATTERN } from '@/constants/patterns';
import useForm from '@/hooks/useForm';
import { submitGetInTouch } from '@/services/server/booking';
import { Campaign } from '@/types/campaign';
import { PetType } from '@/types/properties';
import FormHelperText from '@/ui/atoms/FormHelperText';
import InputLabel from '@/ui/atoms/InputLabel';
import { getSearchQueryParamsFromFilters } from '@/utils/filters';
import { KlaviyoEvents, pushKlaviyoData } from '@/utils/klaviyoAnalytics';
import { getListingSearchQueriesFromURL } from '@/utils/listing-search';

import classNames from 'classnames';
import dayjs from 'dayjs';
import { useRouter } from 'next/navigation';

import GuestAndPetSelector from '../listing-details/GuestAndPetSelector';
import AreaFilter from '../property-search/AreaFilter';
import DateRangePickerInput from '../property-search/GeneralInquiryForm/DateRangePickerInput';
import DateRangePickerMobile from '../property-search/GeneralInquiryForm/DateRangePickerMobile';

type Props = {
  campaign: Campaign;
};

export type FormValues = {
  firstname: string;
  lastname: string;
  email: string;
  phone: string;
  areas: string[];
  amenities: string[];
};

const CampaignForm = ({ campaign }: Props) => {
  const router = useRouter();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [errors, setErrors] = useState<any>({});
  const [date, setDate] = useState<DateRange | undefined>(undefined);
  const [petCount, setPetCount] = useState<number>(1);
  const [petType, setPetType] = useState<PetType>(PetType.DOG);
  const [petDescription, setPetDescription] = useState<string>('');
  const [isPetSelected, setIsPetSelected] = useState<boolean>(false);
  const [guestsValues, setGuestsValues] = useState<GuestsValues>({
    adults: 1,
    children: 0,
  });

  const queryParams = useMemo(() => {
    const searchParams = campaign.listingFilters
      ? new URL(campaign.listingFilters ?? '').searchParams
      : undefined;
    return campaign.listingFilters && searchParams && Object.fromEntries(searchParams?.entries());
  }, [campaign.listingFilters]);

  const { formState, onChange, preSubmitCheck: preSubmitCheckCurrent } = useForm<FormValues>(
    {
      firstname: '',
      lastname: '',
      email: '',
      phone: '',
      areas: [],
      amenities: [],
    },
    {
      firstname: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter a first name`;
        }
      },
      lastname: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter a last name`;
        }
      },
      email: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter an email`;
        }

        if (!_value.match(EMAIL_PATTERN)) {
          return 'Invalid email address';
        }
      },
      phone: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter phone number`;
        }

        if (!_value.match(PHONE_NUMBER_PATTERN)) {
          return 'Invalid phone number';
        }
      },
    },
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange],
  );

  const onChangeNeighborhood = useCallback(
    (value: string[]) => {
      onChange(value, 'areas');
    },
    [onChange],
  );

  const onChangeAmenities = useCallback(
    (value: string[]) => {
      onChange(value, 'amenities');
    },
    [onChange],
  );

  const preSubmitCheck = useCallback(() => {
    const newErrors: any = preSubmitCheckCurrent();
    if (!date?.from || !date?.to) {
      newErrors['date'] = 'Dates are required';
    }

    setErrors(newErrors);
    return newErrors;
  }, [date?.from, date?.to, setErrors, preSubmitCheckCurrent]);

  const onSubmit = useCallback(() => {
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== '')) {
      return;
    }

    if (!date?.from || !date?.to) {
      return;
    }
    setSubmitting(true);
    const hasValidDates = date && dayjs(date.from).isValid() && dayjs(date.to).isValid();

    submitGetInTouch({
      email: formState.email ?? '',
      phone: formState.phone ?? '',
      first_name: formState.firstname ?? '',
      last_name: formState.lastname ?? '',
      guest: guestsValues.adults ?? 1,
      children: guestsValues?.children ?? 0,
      interest: 'rentals',
      arrival_date: hasValidDates ? dayjs(date.from).format('YYYY-MM-DD') : undefined,
      departure_date: hasValidDates ? dayjs(date.to).format('YYYY-MM-DD') : undefined,
      pet_count: isPetSelected ? petCount : undefined,
      pet_type: isPetSelected ? petType : undefined,
      pet_description: isPetSelected
        ? petDescription.length > 0
          ? petDescription
          : 'Pet Description'
        : undefined,
      contact_method: 'email',
      source: 'nr',
      areas: formState?.areas ?? [],
      amenities: formState?.amenities ?? [],
    })
      .then(() => {
        setSubmitting(false);

        toast.success('Your request has been sent successfully');
        pushKlaviyoData(KlaviyoEvents.LEAD_GEN_FORM_SUBMISSION, {
          name,
          email: formState?.email ?? '',
          phone: formState?.phone ?? '',
          dates: [
            hasValidDates ? dayjs(date.from).format('YYYY-MM-DD') : null,
            hasValidDates ? dayjs(date.to).format('YYYY-MM-DD') : null,
          ],
          children: guestsValues?.children,
          adults: guestsValues?.adults,
          number_of_days: hasValidDates ? dayjs(date.to).diff(dayjs(date.from), 'days') : null,
          preferred_amenities: formState?.amenities ?? [],
          preferred_neighborhoods: formState?.areas ?? [],
        });
        setTimeout(() => {
          const queryParams = getSearchQueryParamsFromFilters({
            ...formState,
            guests: { adults: guestsValues.adults, children: guestsValues?.children },
            bedrooms: null,
          });
          router.push(
            `/property-search${queryParams.length > 0 ? '?' : ''}${queryParams.join('&')}`,
          );
        }, 500);
      })
      .catch((e) => {
        console.log(e);
        setSubmitting(false);
      });
  }, [
    preSubmitCheck,
    date,
    formState,
    guestsValues.adults,
    guestsValues?.children,
    isPetSelected,
    petCount,
    petType,
    petDescription,
    router,
  ]);

  return (
    <div className="w-full md:w-[540px] md:rounded-2xl bg-white md:shadow px-4 py-6 mx-auto md:-mt-10">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-4 mb-4">
        <div>
          <InputLabel
            className={classNames('text-xs', !!errors?.firstname?.length && 'text-error')}
          >
            FIRST NAME
          </InputLabel>
          <Input
            type="text"
            name="firstname"
            value={formState.firstname}
            placeholder="Enter your first name"
            className="w-full rounded-full"
            onChange={onChangeTextInput}
            helperText={errors?.firstname ?? ''}
            error={!!errors?.firstname?.length}
          />
        </div>
        <div>
          <InputLabel className={classNames('text-xs', !!errors?.lastname?.length && 'text-error')}>
            LAST NAME
          </InputLabel>
          <Input
            type="text"
            name="lastname"
            value={formState.lastname}
            placeholder="Enter your last name"
            className="w-full rounded-full"
            onChange={onChangeTextInput}
            helperText={errors?.lastname ?? ''}
            error={!!errors?.lastname?.length}
          />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-4 mb-4">
        <div>
          <InputLabel className={classNames('text-xs', !!errors?.email?.length && 'text-error')}>
            EMAIL
          </InputLabel>
          <Input
            type="text"
            name="email"
            value={formState.email}
            placeholder="Enter your email"
            className="w-full rounded-full"
            onChange={onChangeTextInput}
            helperText={errors?.email ?? ''}
            error={!!errors?.email?.length}
          />
        </div>
        <div>
          <InputLabel className={classNames('text-xs', !!errors?.phone?.length && 'text-error')}>
            PHONE
          </InputLabel>
          <Input
            type="text"
            name="phone"
            value={formState.phone}
            placeholder="Enter your phone"
            className="w-full rounded-full"
            onChange={onChangeTextInput}
            helperText={errors?.email ?? ''}
            error={!!errors?.email?.length}
          />
        </div>
      </div>
      <div className="my-4">
        <div className="hidden md:block">
          <DateRangePickerInput labelClassName="text-xs" date={date} setDate={setDate} />
        </div>
        <DateRangePickerMobile labelClassName="text-xs" date={date} setDate={setDate} />
        {errors.date && (
          <div className="ml-2">
            <FormHelperText error>{errors.date}</FormHelperText>
          </div>
        )}
      </div>
      <GuestAndPetSelector
        guestsValues={guestsValues}
        setGuestsValues={setGuestsValues}
        petCount={petCount}
        setPetCount={setPetCount}
        isPetSelected={isPetSelected}
        petType={petType}
        setPetType={setPetType}
        petDescription={petDescription}
        setPetDescription={setPetDescription}
        setIsPetSelected={setIsPetSelected}
        labelClassName="text-xs"
        petsAllowed
      />
      <div className="my-4">
        <InputLabel className="text-xs">AREA</InputLabel>
        <AreaFilter
          areas={formState?.areas ?? []}
          setAreas={onChangeNeighborhood}
          className="relative border border-solid border-disabled rounded-[40px] px-4 py-2.5 text-metal-gray text-sm"
          popoverClassName="md:w-[508px] rounded-2xl"
          popoverContentProps={{ side: 'top', avoidCollisions: true }}
          placeholderText="Any area of the island"
        />
      </div>
      <div className="mb-4">
        <InputLabel className="text-xs">PROPERTY FEATURES</InputLabel>
        <AmenitiesSelector
          amenities={formState?.amenities ?? []}
          setAmenities={onChangeAmenities}
          className="relative border border-solid border-disabled rounded-[40px] px-4 py-2.5 text-metal-gray text-sm"
          popoverClassName="md:w-[508px] rounded-2xl"
          popoverContentProps={{ side: 'top', avoidCollisions: true }}
          placeholderText="Optional"
        />
      </div>
      <Button
        className="w-full rounded-full font-medium h-14"
        onClick={onSubmit}
        disabled={submitting}
        isLoading={submitting}
      >
        <CalendarIcon className="w-5 h-5 mr-2" />
        View Available Rentals
      </Button>
    </div>
  );
};

export default CampaignForm;
