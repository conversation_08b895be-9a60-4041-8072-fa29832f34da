'use client';

import { memo, useCallback, useMemo, useState } from 'react';

import { ChevronDownIcon } from '@heroicons/react/24/outline';
import { PopoverContentProps } from '@radix-ui/react-popover';

import Checkbox from '@/components/ui/checkbox';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

type Props = {
  amenities: string[];
  setAmenities: (_a: string[]) => void;
  className?: string;
  popoverClassName?: string;
  popoverContentProps?: PopoverContentProps;
  placeholderText?: string;
};

const AMENITIES = [
  { id: 'air_conditioning', text: 'Air conditioning', value: 'air_conditioning' },
  { id: 'pool', text: 'Pool', value: 'pool' },
  { id: 'pet_friendly', text: 'Pet Friendly', value: 'pet_friendly' },
  { id: 'water_front', text: 'Waterfront', value: 'water_front' },
  { id: 'water_view', text: 'Waterview', value: 'water_view' },
  { id: 'grill', text: 'Grill', value: 'grill' },
  { id: 'tennis', text: 'Tennis', value: 'tennis' },
  { id: 'king_bed', text: 'King bed', value: 'king_bed' },
];

const AmenitiesSelector = ({
  amenities = [],
  setAmenities,
  className = '',
  popoverClassName = '',
  popoverContentProps,
  placeholderText,
}: Props) => {
  const [open, setOpen] = useState<boolean>(false);

  const onClickedTrigger = useCallback((e: any) => {
    e.preventDefault();
  }, []);

  const onClick = useCallback(() => {
    setOpen(true);
  }, []);

  const onSelectAll = useCallback(() => {
    if (amenities.length === AMENITIES.length) {
      setAmenities([]);
    } else {
      setAmenities(AMENITIES.map((_a) => _a.value));
    }
  }, [amenities.length, setAmenities]);

  const onChange = useCallback(
    (_n: string) => {
      const hasId = amenities.includes(_n);
      setAmenities(hasId ? amenities.filter((_a) => _a !== _n) : [...amenities, _n]);
    },
    [amenities, setAmenities],
  );

  const displayText = useMemo(() => {
    return amenities.length > 0
      ? `${amenities
          .map((_id) => AMENITIES.find((_A) => _A.id === _id)?.text)
          .slice(0, 2)
          .join(', ')}${amenities.length > 2 ? ` and ${amenities.length - 2} more` : ''}`
      : placeholderText ?? 'Area';
  }, [amenities, placeholderText]);

  return (
    <Popover modal open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild onClick={onClickedTrigger}>
        <div
          onClick={onClick}
          onKeyDown={onClick}
          role="button"
          tabIndex={0}
          className={cn(
            'flex items-center justify-between px-2 pt-1.5 pb-2.5 bg-white rounded cursor-pointer gap-x-2',
            className,
          )}
        >
          <p className="truncate max-w-[calc(100%-24px)] m-0">{displayText}</p>
          <ChevronDownIcon className={`w-4 h-4 text-[#6D7381] ${open && 'rotate-180'}`} />
        </div>
      </PopoverTrigger>
      <PopoverContent
        className={cn(
          'min-w-[380px] border border-solid border-english-manor border-opacity-40',
          popoverClassName,
        )}
        side="top"
        align="start"
        avoidCollisions={false}
        {...popoverContentProps}
      >
        <>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="check-all"
              onChange={onSelectAll}
              checked={amenities.length === AMENITIES.length}
            />
            <label htmlFor="check-all" className="cursor-pointer">
              Check All
            </label>
          </div>
          <Separator className="my-2" />
          <div className="max-h-[300px] overflow-y-auto grid grid-cols-2 gap-2">
            {AMENITIES?.map((_n, index) => (
              <div className="py-1 cursor-pointer flex items-center gap-x-2" key={index}>
                <Checkbox
                  checked={amenities.includes(_n.value)}
                  id={_n.id}
                  onChange={() => onChange(_n.value)}
                />
                <label htmlFor={_n.id} className="cursor-pointer">
                  {_n.text}
                </label>
              </div>
            ))}
          </div>
        </>
      </PopoverContent>
    </Popover>
  );
};

export default memo(AmenitiesSelector);
