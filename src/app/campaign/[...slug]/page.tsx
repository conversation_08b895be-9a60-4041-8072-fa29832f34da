import { Suspense } from 'react';

import { ArrowRightIcon } from '@heroicons/react/24/outline';

import CampaignListings from '@/app/views/campaign/CampaignListings';
import CampaignForm from '@/clients/views/campaign/CampaignForm';
import { PROPERTY_SEARCH_ROUTE } from '@/constants/routes';
import { Campaign } from '@/types/campaign';
import { Nullable } from '@/types/common';

import Image from 'next/image';
import Link from 'next/link';
import { notFound } from 'next/navigation';

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';



async function getCampaign<T>(slug: string) {
  console.log({ slug });
  const res = await fetch(`${BASE_URL}/admin/ad-campaigns/${slug}`, {
    next: { revalidate: 60 },
  });
  if (!res.ok) return null;
  return res.json() as T;
}

type PageProps = {
  params: Promise<{ slug: string | string[] }>;
};

export default async function CampaignPage({ params }: PageProps) {
  const { slug } = await params;
  const formattedSlug = (slug as string[]).join('/') ?? '';
  const campaign = await getCampaign<Nullable<Campaign>>(formattedSlug);
  if (!campaign) {
    return notFound();
  }

  return (
    <>
      <header className="relative w-full min-h-[360px] h-max lg:h-[450px] z-[1]">
        <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-black/50 to-black/60 z-[2]" />
        <Image
          src="/images/campaign-bg.jpg"
          alt="Background"
          fill
          priority
          className="object-cover"
        />
        <div className="flex flex-col md:flex-row items-center justify-center gap-2 md:gap-6 absolute left-1/2 -translate-x-1/2 top-10 md:top-[35%] md:-translate-y-[35%] z-10">
          <Image
            alt="NR logo"
            src="/images/nr-logo-2.svg"
            width={0}
            height={0}
            className="w-[172px] md:w-[284px] h-auto"
          />
          <span className="text-lg md:text-[36px] leading-[100%] font-medium tracking-[-0.36px] md:tracking-[-0.72px] text-white">
            by
          </span>
          <Image
            alt="CNC logo"
            src="/images/cnc-white-logo.svg"
            width={0}
            height={0}
            className="w-[200px] md:w-[350px] h-auto"
          />
        </div>
        <div className="p-5 rounded-[12px] w-[96%] md:w-[70%] bg-[rgba(43,60,87,0.8)] absolute left-1/2 -translate-x-1/2 bottom-2 md:bottom-8 text-center z-10">
          <p className="text-xl md:text-[26px] leading-[100%] font-medium tracking-[-0.52px] text-white m-0">
            {campaign.clusterHeadline}
          </p>
          <p className="text-carolina-blue text-xl md:text-[26px] font-medium tracking-[-0.52px] my-2 leading-[100%]">
            {campaign.regionHeadline}
          </p>
          <p className="text-white text-sm m-0">{campaign.clusterText}</p>
        </div>
      </header>
      <main className="relative z-[2] pb-10">
        <CampaignForm campaign={campaign} />
        <div className="container">
          <div className="flex items-center flex-col-reverse md:flex-col gap-4 my-8">
            <div className="flex flex-col md:flex-row items-stretch gap-2 h-max">
              <div className="pt-6 px-8 pb-4 border border-solid border-grey-border rounded-2xl w-full md:w-1/3">
                <div className="w-[120px] h-[120px] rounded-full border-4 border-solid border-white bg-[#D7E9FC] shadow-md flex items-center justify-center mx-auto">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="61"
                    height="61"
                    viewBox="0 0 61 61"
                    fill="none"
                  >
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M40.9583 9.96497V14.1888L45.9583 18.1888V9.96497H40.9583ZM49.7083 21.1888V9.33997C49.7083 7.61408 48.3092 6.21497 46.5833 6.21497H40.3333C38.6074 6.21497 37.2083 7.61408 37.2083 9.33997V11.1888L36.1898 10.374C32.7659 7.63487 27.9007 7.63487 24.4768 10.374L4.16202 26.6258C3.3534 27.2727 3.2223 28.4527 3.86919 29.2613C4.51608 30.0699 5.69601 30.201 6.50462 29.5541L8.45832 27.9911V53.715H5.33332C4.29779 53.715 3.45832 54.5544 3.45832 55.59C3.45832 56.6255 4.29779 57.465 5.33332 57.465H55.3333C56.3689 57.465 57.2083 56.6255 57.2083 55.59C57.2083 54.5544 56.3689 53.715 55.3333 53.715H52.2083V27.9911L54.162 29.5541C54.9706 30.201 56.1506 30.0699 56.7974 29.2613C57.4443 28.4527 57.3132 27.2727 56.5046 26.6258L49.7083 21.1888ZM48.4583 24.9911L33.8472 13.3023C31.7929 11.6588 28.8738 11.6588 26.8194 13.3023L12.2083 24.9911V53.715H20.9583L20.9583 42.9665C20.9582 41.3044 20.9581 39.8722 21.1123 38.7254C21.2776 37.4963 21.6502 36.3181 22.6058 35.3625C23.5615 34.4069 24.7396 34.0342 25.9687 33.869C27.1156 33.7148 28.5477 33.7149 30.2099 33.715H30.4567C32.1189 33.7149 33.5511 33.7148 34.6979 33.869C35.927 34.0342 37.1052 34.4069 38.0608 35.3625C39.0164 36.3181 39.3891 37.4963 39.5543 38.7254C39.7085 39.8722 39.7084 41.3044 39.7083 42.9665L39.7083 53.715H48.4583V24.9911ZM35.9583 53.715V43.09C35.9583 41.2692 35.9543 40.0922 35.8377 39.225C35.7284 38.4118 35.5519 38.1569 35.4091 38.0141C35.2664 37.8714 35.0115 37.6949 34.1982 37.5855C33.3311 37.4689 32.1541 37.465 30.3333 37.465C28.5125 37.465 27.3356 37.4689 26.4684 37.5855C25.6551 37.6949 25.4002 37.8714 25.2575 38.0141C25.1148 38.1569 24.9382 38.4118 24.8289 39.225C24.7123 40.0922 24.7083 41.2692 24.7083 43.09V53.715H35.9583ZM30.3333 21.215C28.6074 21.215 27.2083 22.6141 27.2083 24.34C27.2083 26.0659 28.6074 27.465 30.3333 27.465C32.0592 27.465 33.4583 26.0659 33.4583 24.34C33.4583 22.6141 32.0592 21.215 30.3333 21.215ZM23.4583 24.34C23.4583 20.543 26.5364 17.465 30.3333 17.465C34.1303 17.465 37.2083 20.543 37.2083 24.34C37.2083 28.1369 34.1303 31.215 30.3333 31.215C26.5364 31.215 23.4583 28.1369 23.4583 24.34Z"
                      fill="#15A5E5"
                    />
                  </svg>
                </div>
                <p className="leading-[140%] font-bold m-0 my-4 text-center">
                  More Listings Than Anywhere Else
                </p>
                <p className="leading-[140%] text-sm m-0 text-center">
                  More listings than any other source. Over 1,000 homes, ensuring unparalleled
                  choices for your perfect island getaway.
                </p>
              </div>
              <div className="pt-6 px-8 pb-4  border border-solid border-grey-border rounded-2xl w-full md:w-1/3">
                <div className="w-[120px] h-[120px] rounded-full border-4 border-solid border-white bg-[#D7E9FC] shadow-md flex items-center justify-center mx-auto">
                  <svg
                    width="54"
                    height="55"
                    viewBox="0 0 54 55"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M45.75 41.34C45.75 42.7207 44.6307 43.84 43.25 43.84C41.8693 43.84 40.75 42.7207 40.75 41.34C40.75 39.9593 41.8693 38.84 43.25 38.84C44.6307 38.84 45.75 39.9593 45.75 41.34Z"
                      fill="#15A5E5"
                    />
                    <path
                      d="M13.25 11.34C13.25 12.7207 12.1307 13.84 10.75 13.84C9.36929 13.84 8.25 12.7207 8.25 11.34C8.25 9.95925 9.36929 8.83997 10.75 8.83997C12.1307 8.83997 13.25 9.95925 13.25 11.34Z"
                      fill="#15A5E5"
                    />
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M0.125 10.7333C0.125 5.07569 5.01115 0.714966 10.75 0.714966C16.4889 0.714966 21.375 5.07569 21.375 10.7333C21.375 15.7989 18.2605 21.7777 13.0869 23.9886C11.6005 24.6238 9.89947 24.6238 8.4131 23.9886C3.23952 21.7777 0.125 15.7989 0.125 10.7333ZM10.75 4.46497C6.82387 4.46497 3.875 7.39599 3.875 10.7333C3.875 14.5919 6.34587 19.0271 9.88673 20.5403C10.4318 20.7732 11.0682 20.7732 11.6133 20.5403C15.1541 19.0271 17.625 14.5919 17.625 10.7333C17.625 7.39599 14.6761 4.46497 10.75 4.46497ZM25.125 10.09C25.125 9.05443 25.9645 8.21497 27 8.21497H37.3297C44.2078 8.21497 46.8235 17.1975 41.0207 20.8901L14.9926 37.4535C12.3549 39.132 13.5439 43.215 16.6703 43.215H22.4733L21.9242 42.6658C21.1919 41.9336 21.1919 40.7464 21.9242 40.0141C22.6564 39.2819 23.8436 39.2819 24.5758 40.0141L28.3258 43.7641C29.0581 44.4964 29.0581 45.6836 28.3258 46.4158L24.5758 50.1658C23.8436 50.898 22.6564 50.898 21.9242 50.1658C21.1919 49.4336 21.1919 48.2464 21.9242 47.5141L22.4733 46.965H16.6703C9.79219 46.965 7.17654 37.9824 12.9793 34.2898L39.0074 17.7264C41.6451 16.0479 40.4561 11.965 37.3297 11.965H27C25.9645 11.965 25.125 11.1255 25.125 10.09ZM32.625 40.7333C32.625 35.0757 37.5111 30.715 43.25 30.715C48.9888 30.715 53.875 35.0757 53.875 40.7333C53.875 45.7989 50.7605 51.7777 45.5869 53.9886C44.1005 54.6238 42.3995 54.6238 40.9131 53.9886C35.7395 51.7777 32.625 45.7989 32.625 40.7333ZM43.25 34.465C39.3239 34.465 36.375 37.396 36.375 40.7333C36.375 44.5919 38.8459 49.0271 42.3867 50.5403C42.9318 50.7732 43.5682 50.7732 44.1133 50.5403C47.6541 49.0271 50.125 44.5919 50.125 40.7333C50.125 37.396 47.1761 34.465 43.25 34.465Z"
                      fill="#15A5E5"
                    />
                  </svg>
                </div>
                <p className="leading-[140%] font-bold m-0 my-4 text-center">
                  Local Expertise (from actual locals..)
                </p>
                <p className="leading-[140%] text-sm m-0 text-center">
                  Work with local experts to guide your journey, offering personalized insights on
                  rentals, dining, beaches, and more.
                </p>
              </div>
              <div className="pt-6 px-8 pb-4 border border-solid border-grey-border rounded-2xl w-full md:w-1/3">
                <div className="w-[120px] h-[120px] rounded-full border-4 border-solid border-white bg-[#D7E9FC] shadow-md flex items-center justify-center mx-auto">
                  <svg
                    width="55"
                    height="48"
                    viewBox="0 0 55 48"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M44.712 15.2709C45.7166 15.0198 46.7346 15.6306 46.9858 16.6352C47.2369 17.6398 46.6261 18.6578 45.6215 18.9089L35.6215 21.4089C34.6169 21.6601 33.5989 21.0493 33.3477 20.0447C33.0966 19.0401 33.7074 18.0221 34.712 17.7709L44.712 15.2709Z"
                      fill="#15A5E5"
                    />
                    <path
                      d="M44.712 25.2709C45.7166 25.0198 46.7346 25.6306 46.9858 26.6352C47.2369 27.6398 46.6261 28.6578 45.6215 28.9089L35.6215 31.4089C34.6169 31.6601 33.5989 31.0493 33.3477 30.0447C33.0966 29.0401 33.7074 28.0221 34.712 27.7709L44.712 25.2709Z"
                      fill="#15A5E5"
                    />
                    <path
                      d="M8.34773 16.6352C8.59888 15.6306 9.61689 15.0198 10.6215 15.2709L20.6215 17.7709C21.6261 18.0221 22.2369 19.0401 21.9858 20.0447C21.7346 21.0493 20.7166 21.6601 19.712 21.4089L9.71199 18.9089C8.70738 18.6578 8.09658 17.6398 8.34773 16.6352Z"
                      fill="#15A5E5"
                    />
                    <path
                      d="M8.34773 26.6352C8.59888 25.6306 9.61689 25.0198 10.6215 25.2709L20.6215 27.7709C21.6261 28.0221 22.2369 29.0401 21.9858 30.0447C21.7346 31.0493 20.7166 31.6601 19.712 31.4089L9.71199 28.9089C8.70738 28.6578 8.09658 27.6398 8.34773 26.6352Z"
                      fill="#15A5E5"
                    />
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M54.5417 6.92291C54.5417 3.26465 51.6285 0.127301 47.8064 0.260436C44.9606 0.359565 41.2407 0.655604 38.379 1.51231C35.8914 2.25703 33.0934 3.71163 30.9122 4.974C28.8669 6.15765 26.3209 6.21669 24.2488 5.12104C21.7602 3.80508 18.4859 2.24522 15.6451 1.4956C13.2431 0.86175 10.2047 0.5573 7.73777 0.405685C3.84217 0.166261 0.791748 3.33654 0.791748 7.08444V34.9489C0.791748 38.7894 3.90121 41.7878 7.58685 42.0183C9.98375 42.1682 12.6809 42.4566 14.6883 42.9863C17.1697 43.6411 20.2383 45.097 22.7158 46.4176C25.7991 48.0612 29.5344 48.0612 32.6177 46.4176C35.0952 45.097 38.1637 43.6411 40.6451 42.9863C42.6526 42.4566 45.3497 42.1682 47.7466 42.0183C51.4323 41.7878 54.5417 38.7894 54.5417 34.9489V6.92291ZM47.937 4.00816C49.4739 3.95463 50.7917 5.21894 50.7917 6.92291V34.9489C50.7917 36.6633 49.371 38.1594 47.5126 38.2756C45.0623 38.4289 42.0644 38.7334 39.6884 39.3604C36.7625 40.1325 33.3736 41.7652 30.8537 43.1084C30.4331 43.3326 29.993 43.5092 29.5417 43.6382V9.44502C30.6725 9.21866 31.772 8.80913 32.7905 8.21963C34.9167 6.98915 37.4039 5.71865 39.4545 5.10477C41.8274 4.3944 45.1399 4.1056 47.937 4.00816ZM25.7917 9.52738C24.6542 9.35049 23.5392 8.9878 22.4959 8.43612C20.0539 7.14483 17.0936 5.7562 14.6883 5.12148C12.6578 4.58567 9.92269 4.29705 7.50773 4.14862C5.92283 4.05121 4.54175 5.34148 4.54175 7.08444V34.9489C4.54175 36.6633 5.96252 38.1594 7.82094 38.2756C10.2711 38.4289 13.2691 38.7334 15.6451 39.3604C18.571 40.1325 21.9599 41.7652 24.4798 43.1084C24.9004 43.3326 25.3405 43.5092 25.7917 43.6382V9.52738Z"
                      fill="#15A5E5"
                    />
                  </svg>
                </div>
                <p className="leading-[140%] font-bold m-0 my-4 text-center">
                  Book with Confidence
                </p>
                <p className="leading-[140%] text-sm m-0 text-center">
                  Every listings has been vetted for authenticity.
                </p>
              </div>
            </div>
            <div className="md:border md:border-solid md:border-grey-border md:rounded-2xl md:p-6 pb-4 md:pb-0">
              <p className="text-center text-3xl md:text-[50px] leading-[120%] font-semibold m-0">
                {campaign.regionText}
              </p>
            </div>
          </div>
          <Suspense>
            <CampaignListings campaign={campaign} />
            <Link
              href={PROPERTY_SEARCH_ROUTE}
              className="border-solid border-[#15A5E5] text-[#15A5E5] px-[26px] py-4 flex gap-2.5 items-center rounded-full max-w-max m-auto no-underline"
            >
              Explore more
              <ArrowRightIcon className="w-4 h-4" />
            </Link>
          </Suspense>
        </div>
      </main>
    </>
  );
}
