import PropertyCard from '@/app/components/common/PropertyCard';
import PorpertyCardClientWrapper from '@/clients/components/common/PorpertyCardClientWrapper';
import { searchPropertyListings } from '@/services/server/property-search';
import { Campaign } from '@/types/campaign';
import { NRProperties } from '@/types/properties';
import { getListingSearchQueriesFromURL } from '@/utils/listing-search';

type Props = {
  campaign: Campaign;
};

const CampaignListings = async ({ campaign }: Props) => {
  const searchParams = campaign.listingFilters
    ? new URL(campaign.listingFilters ?? '').searchParams
    : undefined;
  const data = await searchPropertyListings<{ results: NRProperties[] }>(
    undefined,
    campaign.listingFilters && searchParams
      ? `${getListingSearchQueriesFromURL(
          Object.fromEntries(searchParams.entries()),
        )}&exact_match=1`
      : 'exact_match=1',
  );

  return (
    <div className="flex items-center flex-wrap lg:flex-nowrap gap-x-8 gap-y-[60px] mb-[40px] overflow-x-scroll overflow-y-hidden scrollbar-hide">
      {(data?.results ?? []).slice(0, 3).map((_property, index) => (
        <PorpertyCardClientWrapper key={index} propertyDetails={_property}>
          <PropertyCard propertyDetails={_property} className="" />
        </PorpertyCardClientWrapper>
      ))}
    </div>
  );
};

export default CampaignListings;
